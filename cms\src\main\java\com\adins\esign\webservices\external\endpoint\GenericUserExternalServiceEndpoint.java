package com.adins.esign.webservices.external.endpoint;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.message.Message;
import org.apache.cxf.phase.PhaseInterceptorChain;
import org.apache.cxf.transport.http.AbstractHTTPDestination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.InvitationLinkLogic;
import com.adins.esign.businesslogic.api.RegistrationLogic;
import com.adins.esign.businesslogic.api.UserExternalLogic;
import com.adins.esign.businesslogic.api.UserLogic;
import com.adins.esign.constants.HttpHeaders;
import com.adins.esign.webservices.external.api.UserExternalService;
import com.adins.esign.webservices.model.CheckRegistrationRequest;
import com.adins.esign.webservices.model.CheckRegistrationResponse;
import com.adins.esign.webservices.model.RequestSentOtpSigningRequest;
import com.adins.esign.webservices.model.RequestSentOtpSigningResponse;
import com.adins.esign.webservices.model.external.CheckVerificationStatusExternalRequest;
import com.adins.esign.webservices.model.external.CheckVerificationStatusExternalResponse;
import com.adins.esign.webservices.model.external.DownloadUserCertificateRequest;
import com.adins.esign.webservices.model.external.DownloadUserCertificateResponse;
import com.adins.esign.webservices.model.external.GeneratInvLinkExternalRequest;
import com.adins.esign.webservices.model.external.GeneratInvLinkExternalResponse;
import com.adins.esign.webservices.model.external.GetActivationLinkRequest;
import com.adins.esign.webservices.model.external.GetActivationLinkResponse;
import com.adins.esign.webservices.model.external.RegisterExternalRequest;
import com.adins.esign.webservices.model.external.RegisterExternalResponse;
import com.adins.esign.webservices.model.external.UpdateDataSignerExternalRequest;
import com.adins.esign.webservices.model.external.UpdateDataSignerExternalResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/external/user")
@Api(value = "UserExternalService")
@Produces({MediaType.APPLICATION_JSON})
public class GenericUserExternalServiceEndpoint implements UserExternalService {

	@Autowired private RegistrationLogic registrationLogic;
	@Autowired private UserLogic userLogic;
	@Autowired private InvitationLinkLogic invitationLinkLogic;
	@Autowired private UserExternalLogic userExternalLogic;

	@Override
	@POST
	@Path("/register")
	public RegisterExternalResponse register(RegisterExternalRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();

		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String apiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);

		return registrationLogic.registerExternal(request, apiKey, audit);
	}

	@Override
	@POST
	@Path("/sentOtpSigning")
	public RequestSentOtpSigningResponse requestSentOtpSigning(RequestSentOtpSigningRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return userExternalLogic.sendOtpExternal(request, xApiKey, audit);
	}

	@Override
	@POST
	@Path("/checkRegistration")
	public CheckRegistrationResponse checkRegistration(CheckRegistrationRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String apiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);

		return userLogic.checkRegistration(request, apiKey, audit);
	}

	@Override
	@POST
	@Path("/generateInvLink")
	public GeneratInvLinkExternalResponse generateInvLink(GeneratInvLinkExternalRequest request) throws Exception {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String apiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return invitationLinkLogic.generateInvLinkExternal(request, apiKey, audit);
	}

	@Override
	@POST
	@Path("/checkVerificationStatus")
	public CheckVerificationStatusExternalResponse checkVerificationStatusExternal(CheckVerificationStatusExternalRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String apiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return userExternalLogic.checkVerificationStatusExternal(request, apiKey, audit);
	}

	@Override
	@POST
	@Path("/getActivationLink")
	public GetActivationLinkResponse getActivationLinkExternal(GetActivationLinkRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String apiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return userExternalLogic.getActivationLinkExternal(request, apiKey, audit);
	}
	@Override
	@POST
	@Path("/downloadUserCertificate")
	public DownloadUserCertificateResponse downloadUserCertificate(DownloadUserCertificateRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return userLogic.downloadUserCertificate(request, xApiKey, audit);
	}

	@Override
	@POST
	@Path("/updateDataSigner")
	public UpdateDataSignerExternalResponse updateDataSignerExternal(UpdateDataSignerExternalRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		String xRealIp = httpRequest.getHeader("x-real-ip");
		if (StringUtils.isNotBlank(xRealIp)) {
			request.setIpAddress(xRealIp);
		}
		return userExternalLogic.updateDataSignerExternal(request, xApiKey, audit);
	}

}
