package com.adins.esign.webservices.model.external;

import com.adins.framework.service.base.model.MssRequestType;

public class UpdateDataSignerExternalRequest extends MssRequestType {
	private static final long serialVersionUID = 1L;
	
	private String psreCode;
	private String nik;
	private String fullName;
	private String gender;
	private String kelurahan;
	private String kecamatan;
	private String kota;
	private String zipCode;
	private String dateOfBirth;
	private String placeOfBirth;
	private String provinsi;
	private String phoneNo;
	private String address;
	private String email;
	private String ipAddress;
	
	public String getPsreCode() {
		return psreCode;
	}
	
	public void setPsreCode(String psreCode) {
		this.psreCode = psreCode;
	}
	
	public String getNik() {
		return nik;
	}
	
	public void setNik(String nik) {
		this.nik = nik;
	}
	
	public String getFullName() {
		return fullName;
	}
	
	public void setFullName(String fullName) {
		this.fullName = fullName;
	}
	
	public String getGender() {
		return gender;
	}
	
	public void setGender(String gender) {
		this.gender = gender;
	}
	
	public String getKelurahan() {
		return kelurahan;
	}
	
	public void setKelurahan(String kelurahan) {
		this.kelurahan = kelurahan;
	}
	
	public String getKecamatan() {
		return kecamatan;
	}
	
	public void setKecamatan(String kecamatan) {
		this.kecamatan = kecamatan;
	}
	
	public String getKota() {
		return kota;
	}
	
	public void setKota(String kota) {
		this.kota = kota;
	}
	
	public String getZipCode() {
		return zipCode;
	}
	
	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}
	
	public String getDateOfBirth() {
		return dateOfBirth;
	}
	
	public void setDateOfBirth(String dateOfBirth) {
		this.dateOfBirth = dateOfBirth;
	}
	
	public String getPlaceOfBirth() {
		return placeOfBirth;
	}
	
	public void setPlaceOfBirth(String placeOfBirth) {
		this.placeOfBirth = placeOfBirth;
	}
	
	public String getProvinsi() {
		return provinsi;
	}
	
	public void setProvinsi(String provinsi) {
		this.provinsi = provinsi;
	}
	
	public String getPhoneNo() {
		return phoneNo;
	}
	
	public void setPhoneNo(String phoneNo) {
		this.phoneNo = phoneNo;
	}
	
	public String getAddress() {
		return address;
	}
	
	public void setAddress(String address) {
		this.address = address;
	}
	
	public String getEmail() {
		return email;
	}
	
	public void setEmail(String email) {
		this.email = email;
	}
	
	public String getIpAddress() {
		return ipAddress;
	}
	
	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}
}
